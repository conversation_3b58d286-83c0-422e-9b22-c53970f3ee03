import { useEffect, useState, useRef } from 'react';
import { Text, View, TouchableOpacity, Dimensions } from 'react-native';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import Modal from 'react-native-modal';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  runOnJS,
  clamp,
} from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { formatSocialTime } from '@/src/utilities/datetime';
import Close from '@/src/assets/svgs/Close';
import type { PostMediaI } from '@/src/networks/content/types';
import Carousel from '../Carousel';
import UserAvatar from '../UserAvatar';
import VideoPlayer from '../VideoPlayer';
import type { ImageViewerModalProps } from './types';

const ZoomableMedia = ({ item }: { item: PostMediaI; index: number }) => {
  const scale = useSharedValue(1);
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);

  const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

  const extension = item.extension ?? item.fileUrl?.split('.').pop()?.toLowerCase();
  const isVideo =
    extension && ['mp4', 'mov', 'avi', 'mkv', 'webm'].includes(extension.toLowerCase());

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { translateX: translateX.value },
      { translateY: translateY.value },
      { scale: scale.value },
    ],
  }));

  const resetTransform = () => {
    'worklet';
    scale.value = withSpring(1, { damping: 20, stiffness: 300 });
    translateX.value = withSpring(0, { damping: 20, stiffness: 300 });
    translateY.value = withSpring(0, { damping: 20, stiffness: 300 });
  };

  const clampTranslation = (currentScale: number) => {
    'worklet';
    const maxTranslateX = Math.max(0, (screenWidth * (currentScale - 1)) / 2);
    const maxTranslateY = Math.max(0, (screenHeight * (currentScale - 1)) / 2);

    translateX.value = clamp(translateX.value, -maxTranslateX, maxTranslateX);
    translateY.value = clamp(translateY.value, -maxTranslateY, maxTranslateY);
  };

  const gesture = (() => {
    let savedScale = 1;
    let savedTranslateX = 0;
    let savedTranslateY = 0;

    const pinchGesture = Gesture.Pinch()
      .onStart(() => {
        savedScale = scale.value;
        savedTranslateX = translateX.value;
        savedTranslateY = translateY.value;
      })
      .onUpdate((e) => {
        const newScale = clamp(savedScale * e.scale, 0.5, 5);
        scale.value = newScale;

        // Apply focal point scaling
        const deltaX = (e.focalX - screenWidth / 2) * (newScale - savedScale);
        const deltaY = (e.focalY - screenHeight / 2) * (newScale - savedScale);

        translateX.value = savedTranslateX - deltaX;
        translateY.value = savedTranslateY - deltaY;

        clampTranslation(newScale);
      })
      .onEnd(() => {
        if (scale.value < 1) {
          runOnJS(resetTransform)();
        } else if (scale.value > 4) {
          scale.value = withSpring(4, { damping: 20, stiffness: 300 });
          clampTranslation(4);
        } else {
          clampTranslation(scale.value);
        }
      });

    const panGesture = Gesture.Pan()
      .onStart(() => {
        savedTranslateX = translateX.value;
        savedTranslateY = translateY.value;
      })
      .onUpdate((e) => {
        translateX.value = savedTranslateX + e.translationX;
        translateY.value = savedTranslateY + e.translationY;
        clampTranslation(scale.value);
      })
      .onEnd(() => {
        if (scale.value <= 1) {
          translateX.value = withSpring(0, { damping: 20, stiffness: 300 });
          translateY.value = withSpring(0, { damping: 20, stiffness: 300 });
        } else {
          clampTranslation(scale.value);
        }
      })
      .minDistance(10)
      .enabled(true);

    const doubleTapGesture = Gesture.Tap()
      .numberOfTaps(2)
      .onEnd((e) => {
        if (scale.value > 1.1) {
          runOnJS(resetTransform)();
        } else {
          const targetScale = 2.5;
          scale.value = withSpring(targetScale, { damping: 20, stiffness: 300 });

          // Zoom into the tapped point
          const deltaX = (e.x - screenWidth / 2) * (targetScale - 1);
          const deltaY = (e.y - screenHeight / 2) * (targetScale - 1);

          translateX.value = withSpring(-deltaX, { damping: 20, stiffness: 300 });
          translateY.value = withSpring(-deltaY, { damping: 20, stiffness: 300 });
        }
      });

    return Gesture.Race(doubleTapGesture, Gesture.Simultaneous(pinchGesture, panGesture));
  })();

  return (
    <View className="w-full h-full">
      <View className="w-full justify-center items-center flex-1">
        {isVideo ? (
          <View className="w-full h-full justify-center items-center">
            <VideoPlayer
              source={item.fileUrl}
              width="100%"
              height="100%"
              showControls={true}
              resizeMode="contain"
              borderRadius={0}
              controlButtonSize={10}
            />
          </View>
        ) : (
          <GestureDetector gesture={gesture}>
            <Animated.View className="w-full h-full justify-center items-center">
              <Animated.Image
                source={{ uri: item.fileUrl }}
                className="w-full h-full"
                resizeMode="contain"
                style={animatedStyle}
              />
            </Animated.View>
          </GestureDetector>
        )}
      </View>
    </View>
  );
};

const ImageViewer = ({ isVisible, onClose, post, initialIndex = 0 }: ImageViewerModalProps) => {
  const timeAgo = formatSocialTime(post.createdAt);
  const insets = useSafeAreaInsets();
  const hasSingleMedia = post.Media?.length === 1;

  const [internalModalVisible, setInternalModalVisible] = useState(false);
  const deferredOnCloseAction = useRef<(() => void) | null>(null);

  useEffect(() => {
    if (isVisible) {
      setInternalModalVisible(true);
      deferredOnCloseAction.current = null;
    } else {
      setInternalModalVisible(false);
    }
  }, [isVisible]);

  const handleCloseInitiated = () => {
    deferredOnCloseAction.current = onClose;
    setInternalModalVisible(false);
  };

  const handleModalHide = () => {
    if (deferredOnCloseAction.current) {
      deferredOnCloseAction.current();
      deferredOnCloseAction.current = null;
    }
  };

  return (
    <Modal
      isVisible={internalModalVisible}
      onBackdropPress={handleCloseInitiated}
      onBackButtonPress={handleCloseInitiated}
      onModalHide={handleModalHide}
      style={{ margin: 0 }}
      animationIn="fadeIn"
      animationOut="fadeOut"
      backdropOpacity={1}
      backdropColor="black"
      animationInTiming={250}
      animationOutTiming={250}
      backdropTransitionInTiming={250}
      backdropTransitionOutTiming={1}
      statusBarTranslucent
      useNativeDriver={false}
      useNativeDriverForBackdrop={false}
      hideModalContentWhileAnimating={false}
      avoidKeyboard
    >
      <View className="flex-1 bg-black" style={{ paddingTop: insets.top }}>
        <View className="flex-row items-center justify-between px-4 py-2">
          <View className="flex-row items-center flex-1">
            <UserAvatar
              avatarUri={post.Profile.avatar}
              name={post.Profile.name}
              width={40}
              height={40}
              className="mr-3"
            />
            <View className="flex-1 ml-3">
              <Text className="text-white font-bold text-base">{post.Profile.name}</Text>
              <Text className="text-gray-300 text-xs" numberOfLines={1}>
                {post.Profile.designation?.name}{' '}
                {post.Profile.entity?.name ? `at ${post.Profile.entity.name}` : ''}
              </Text>
              <Text className="text-gray-400 text-xs">{timeAgo}</Text>
            </View>
          </View>
          <TouchableOpacity onPress={handleCloseInitiated} className="p-2">
            <Close stroke="white" width={2} height={2} />
          </TouchableOpacity>
        </View>
        <View className="flex-1 justify-center items-center">
          {hasSingleMedia ? (
            <ZoomableMedia item={post.Media[0]} index={0} />
          ) : (
            <Carousel
              showArrows={true}
              showDots={false}
              showSlideNumbers={true}
              activeColor="#FFFFFF"
              inactiveColor="#FFFFFF50"
              arrowClassName="bg-white/70 p-3 rounded-full"
              dotClassName="h-3 w-3"
              autoPlay={false}
              initialIndex={initialIndex}
            >
              {post.Media?.map((media, index) => (
                <ZoomableMedia key={index} item={media} index={index} />
              ))}
            </Carousel>
          )}
        </View>
      </View>
    </Modal>
  );
};

export default ImageViewer;
